"""
Simple Tkinter UI for Pausable Contact Scraper
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
import threading
import json
import os
import subprocess
import time
from datetime import datetime
from pathlib import Path

class SimpleScraperUI:
    """Simple Tkinter UI for the pausable contact scraper."""
    
    def __init__(self, root):
        print("🎨 Initializing UI...")
        self.root = root
        self.root.title("🔍 Lead Contact Scraper")
        self.root.geometry("800x600")
        self.root.configure(bg='#f0f0f0')

        # Make sure window appears on top and is visible
        self.root.lift()
        self.root.attributes('-topmost', True)
        self.root.after_idle(lambda: self.root.attributes('-topmost', False))

        # State variables
        self.chunks_dir = None
        self.results_dir = None
        self.is_running = False
        self.monitor_thread = None

        print("📋 Creating widgets...")
        # Create UI
        self.create_widgets()
        print("📂 Loading sessions...")
        self.load_available_sessions()

        print("👀 Starting monitoring...")
        # Start monitoring
        self.start_monitoring()

        print("✅ UI initialization complete!")
    
    def create_widgets(self):
        """Create the UI widgets."""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = ttk.Label(main_frame, text="🔍 Lead Contact Scraper", 
                               font=('Arial', 16, 'bold'))
        title_label.pack(pady=(0, 20))
        
        # Session selection frame
        session_frame = ttk.Frame(main_frame)
        session_frame.pack(fill=tk.X, pady=10)
        
        ttk.Label(session_frame, text="Chunks Directory:").pack(side=tk.LEFT)
        self.chunks_var = tk.StringVar()
        self.chunks_combo = ttk.Combobox(session_frame, textvariable=self.chunks_var, 
                                        state="readonly", width=50)
        self.chunks_combo.pack(side=tk.LEFT, padx=(10, 5), fill=tk.X, expand=True)
        self.chunks_combo.bind('<<ComboboxSelected>>', self.on_session_selected)
        
        ttk.Button(session_frame, text="Browse", command=self.browse_chunks_dir).pack(side=tk.RIGHT)
        
        # Control buttons frame
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=20)
        
        self.start_btn = ttk.Button(control_frame, text="▶️ Start Scraping", 
                                   command=self.start_scraping)
        self.start_btn.pack(side=tk.LEFT, padx=5)
        
        self.pause_btn = ttk.Button(control_frame, text="⏸️ Pause", 
                                   command=self.pause_scraping, state=tk.DISABLED)
        self.pause_btn.pack(side=tk.LEFT, padx=5)
        
        self.resume_btn = ttk.Button(control_frame, text="▶️ Resume", 
                                    command=self.resume_scraping, state=tk.DISABLED)
        self.resume_btn.pack(side=tk.LEFT, padx=5)
        
        self.combine_btn = ttk.Button(control_frame, text="🔗 Combine Results", 
                                     command=self.combine_results)
        self.combine_btn.pack(side=tk.RIGHT, padx=5)
        
        # Progress frame
        progress_frame = ttk.LabelFrame(main_frame, text="Progress", padding="10")
        progress_frame.pack(fill=tk.X, pady=10)
        
        # Progress info
        self.progress_label = ttk.Label(progress_frame, text="Ready - Select chunks directory to begin")
        self.progress_label.pack(anchor=tk.W)
        
        self.overall_progress = ttk.Progressbar(progress_frame, mode='determinate')
        self.overall_progress.pack(fill=tk.X, pady=5)
        
        # Statistics frame
        stats_frame = ttk.Frame(progress_frame)
        stats_frame.pack(fill=tk.X, pady=5)
        
        self.urls_label = ttk.Label(stats_frame, text="URLs: 0")
        self.urls_label.pack(side=tk.LEFT)
        
        self.emails_label = ttk.Label(stats_frame, text="📧 Emails: 0")
        self.emails_label.pack(side=tk.LEFT, padx=(20, 0))
        
        self.phones_label = ttk.Label(stats_frame, text="📞 Phones: 0")
        self.phones_label.pack(side=tk.LEFT, padx=(20, 0))
        
        # Log frame
        log_frame = ttk.LabelFrame(main_frame, text="Activity Log", padding="5")
        log_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, wrap=tk.WORD)
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
        # Status bar
        self.status_var = tk.StringVar(value="Ready")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, 
                              relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(fill=tk.X, pady=(10, 0))
    
    def log_message(self, message):
        """Add message to log."""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def load_available_sessions(self):
        """Load available chunk directories."""
        chunk_dirs = [d for d in os.listdir('.') if d.endswith('_chunks') and os.path.isdir(d)]
        self.chunks_combo['values'] = chunk_dirs
        
        if chunk_dirs:
            self.chunks_combo.set(chunk_dirs[0])
            self.on_session_selected()
    
    def browse_chunks_dir(self):
        """Browse for chunks directory."""
        directory = filedialog.askdirectory(title="Select Chunks Directory")
        if directory:
            self.chunks_var.set(directory)
            self.on_session_selected()
    
    def on_session_selected(self, event=None):
        """Handle session selection."""
        chunks_dir = self.chunks_var.get()
        if not chunks_dir or not os.path.exists(chunks_dir):
            return
        
        self.chunks_dir = chunks_dir
        self.results_dir = f"{chunks_dir}_results"
        
        # Load manifest to get chunk info
        try:
            manifest_path = os.path.join(chunks_dir, 'chunk_manifest.json')
            with open(manifest_path, 'r') as f:
                manifest = json.load(f)
            
            total_chunks = manifest['num_chunks']
            total_records = manifest['total_records']
            
            self.log_message(f"Selected: {chunks_dir}")
            self.log_message(f"Total chunks: {total_chunks}, Total URLs: {total_records:,}")
            
            # Update progress
            self.overall_progress['maximum'] = total_chunks
            self.progress_label.config(text=f"Ready - {total_chunks} chunks, {total_records:,} URLs")
            
            # Load existing progress if available
            self.load_progress()
            
            self.status_var.set(f"Ready - {total_chunks} chunks, {total_records:,} URLs")
            
        except Exception as e:
            self.log_message(f"Error loading session: {e}")
            self.status_var.set("Error loading session")
    
    def load_progress(self):
        """Load existing progress."""
        if not self.results_dir:
            return
        
        progress_file = os.path.join(self.results_dir, 'scraping_progress.json')
        if os.path.exists(progress_file):
            try:
                with open(progress_file, 'r') as f:
                    progress = json.load(f)
                
                completed = len(progress.get('completed_chunks', []))
                total_processed = progress.get('total_processed', 0)
                emails_found = progress.get('total_with_emails', 0)
                phones_found = progress.get('total_with_phones', 0)
                
                self.overall_progress['value'] = completed
                self.urls_label.config(text=f"URLs: {total_processed:,}")
                self.emails_label.config(text=f"📧 Emails: {emails_found:,}")
                self.phones_label.config(text=f"📞 Phones: {phones_found:,}")
                
                # Check if paused
                pause_file = os.path.join(self.results_dir, 'PAUSE_SCRAPING')
                if os.path.exists(pause_file):
                    self.status_var.set("Paused - Click Resume to continue")
                    self.resume_btn.config(state=tk.NORMAL)
                    self.pause_btn.config(state=tk.DISABLED)
                
                self.log_message(f"Loaded progress: {completed} chunks completed")
                
            except Exception as e:
                self.log_message(f"Error loading progress: {e}")
    
    def start_scraping(self):
        """Start the scraping process."""
        if not self.chunks_dir:
            messagebox.showerror("Error", "Please select a chunks directory first")
            return
        
        if not os.path.exists(self.chunks_dir):
            messagebox.showerror("Error", f"Chunks directory not found: {self.chunks_dir}")
            return
        
        # Check if manifest exists
        manifest_path = os.path.join(self.chunks_dir, 'chunk_manifest.json')
        if not os.path.exists(manifest_path):
            messagebox.showerror("Error", f"Chunk manifest not found: {manifest_path}")
            return
        
        self.is_running = True
        self.start_btn.config(state=tk.DISABLED)
        self.pause_btn.config(state=tk.NORMAL)
        self.resume_btn.config(state=tk.DISABLED)
        
        self.status_var.set("Starting scraper...")
        self.log_message("🚀 Starting contact extraction...")
        
        # Start scraper as subprocess with real-time output
        try:
            # Use subprocess to run the scraper
            cmd = ["python", "pausable_scraper.py"]
            self.scraper_process = subprocess.Popen(
                cmd,
                cwd=os.getcwd(),
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,  # Combine stderr with stdout
                text=True,
                bufsize=1,
                universal_newlines=True
            )

            self.log_message("Scraper process started - streaming output...")

            # Start thread to read output in real-time
            self.output_thread = threading.Thread(target=self.read_scraper_output, daemon=True)
            self.output_thread.start()

        except Exception as e:
            self.log_message(f"Error starting scraper: {e}")
            self.scraping_error(str(e))

    def read_scraper_output(self):
        """Read scraper output in real-time and display in log."""
        try:
            while True:
                if self.scraper_process.poll() is not None:
                    # Process has finished
                    break

                line = self.scraper_process.stdout.readline()
                if line:
                    # Remove timestamp and clean up the line
                    clean_line = line.strip()
                    if clean_line:
                        # Schedule UI update on main thread
                        self.root.after(0, lambda msg=clean_line: self.log_scraper_message(msg))
                else:
                    break

            # Process finished
            return_code = self.scraper_process.poll()
            if return_code == 0:
                self.root.after(0, lambda: self.log_message("✅ Scraping completed successfully!"))
                self.root.after(0, self.scraping_completed)
            else:
                self.root.after(0, lambda: self.log_message(f"❌ Scraper exited with code {return_code}"))
                self.root.after(0, lambda: self.scraping_error(f"Process exited with code {return_code}"))

        except Exception as e:
            self.root.after(0, lambda: self.log_message(f"❌ Error reading scraper output: {e}"))

    def log_scraper_message(self, message):
        """Log message from scraper without timestamp (scraper adds its own)."""
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()

        # Update progress if we can parse the message
        if "Batch" in message and "URLs" in message:
            self.status_var.set("Processing batch...")
        elif "Completed in" in message:
            self.status_var.set("Batch completed")
        elif "emails found" in message.lower():
            # Try to extract numbers for progress update
            self.load_progress()

    def scraping_completed(self):
        """Handle scraping completion."""
        self.is_running = False
        self.start_btn.config(state=tk.NORMAL)
        self.pause_btn.config(state=tk.DISABLED)

        self.status_var.set("Scraping completed!")
        self.log_message("🎉 Contact extraction completed!")

        # Load final progress
        self.load_progress()

        # Ask if user wants to combine results
        if messagebox.askyesno("Complete", "Scraping completed! Would you like to combine results now?"):
            self.combine_results()

    def pause_scraping(self):
        """Pause the scraping process."""
        if not self.results_dir:
            return
        
        pause_file = os.path.join(self.results_dir, 'PAUSE_SCRAPING')
        with open(pause_file, 'w') as f:
            f.write(f"Scraping paused at: {datetime.now().isoformat()}\n")
        
        self.pause_btn.config(state=tk.DISABLED)
        self.resume_btn.config(state=tk.NORMAL)
        self.status_var.set("Pausing scraper...")
        self.log_message("⏸️ Pause requested - will pause after current chunk")
    
    def resume_scraping(self):
        """Resume the scraping process."""
        if not self.results_dir:
            return
        
        pause_file = os.path.join(self.results_dir, 'PAUSE_SCRAPING')
        if os.path.exists(pause_file):
            os.remove(pause_file)
        
        self.resume_btn.config(state=tk.DISABLED)
        self.status_var.set("Resuming...")
        self.log_message("▶️ Pause file removed - ready to resume")
        
        # Start scraping again
        self.start_scraping()
    
    def combine_results(self):
        """Combine results using the results combiner."""
        if not self.results_dir or not os.path.exists(self.results_dir):
            messagebox.showerror("Error", "No results directory found")
            return
        
        try:
            self.log_message("🔗 Combining results...")
            self.status_var.set("Combining results...")
            
            # Run results combiner as subprocess
            cmd = ["python", "results_combiner.py"]
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=os.getcwd())
            
            if result.returncode == 0:
                self.log_message("✅ Results combined successfully!")
                self.status_var.set("Results combined!")
                messagebox.showinfo("Success", "Results combined successfully!\nCheck current directory for output files.")
            else:
                self.log_message(f"❌ Error combining results: {result.stderr}")
                messagebox.showerror("Error", f"Error combining results:\n{result.stderr}")
            
        except Exception as e:
            self.log_message(f"❌ Error combining results: {e}")
            messagebox.showerror("Error", f"Error combining results:\n{e}")
    
    def scraping_error(self, error_msg):
        """Handle scraping error."""
        self.is_running = False
        self.start_btn.config(state=tk.NORMAL)
        self.pause_btn.config(state=tk.DISABLED)
        
        self.status_var.set("Error occurred")
        self.log_message(f"❌ Error: {error_msg}")
        
        messagebox.showerror("Scraping Error", f"An error occurred:\n{error_msg}")
    
    def start_monitoring(self):
        """Start monitoring progress in background."""
        def monitor():
            while True:
                try:
                    if self.results_dir and os.path.exists(self.results_dir):
                        self.root.after(0, self.load_progress)
                    time.sleep(5)  # Update every 5 seconds
                except:
                    break
        
        self.monitor_thread = threading.Thread(target=monitor, daemon=True)
        self.monitor_thread.start()

def main():
    """Main function to run the UI."""
    print("🚀 Starting Lead Contact Scraper UI...")
    print("📂 Current directory:", os.getcwd())

    # Check for chunk directories
    chunk_dirs = [d for d in os.listdir('.') if d.endswith('_chunks') and os.path.isdir(d)]
    print(f"📦 Found {len(chunk_dirs)} chunk directories: {chunk_dirs}")

    root = tk.Tk()

    # Configure ttk styles
    style = ttk.Style()
    style.theme_use('clam')

    try:
        print("🎨 Creating UI...")
        app = SimpleScraperUI(root)
        print("✅ UI created successfully! Starting main loop...")
        root.mainloop()
    except KeyboardInterrupt:
        print("Application interrupted")
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        messagebox.showerror("Application Error", f"An error occurred:\n{e}")

    print("👋 UI closed.")

if __name__ == "__main__":
    main()
