"""
Pausable Contact Scraper - Process chunks with pause/resume capability
"""

import asyncio
import json
import os
import pandas as pd
import signal
import sys
from datetime import datetime
from typing import Dict, List, Optional
from pathlib import Path

# Import the perfect contact extractor
from aganl.perfect_contact_extractor import PerfectContactExtractor

class PausableScraper:
    """Pausable scraper that can process chunks and resume from where it left off."""
    
    def __init__(self, chunks_dir: str, results_dir: str = None):
        self.chunks_dir = chunks_dir
        self.results_dir = results_dir or f"{chunks_dir}_results"
        self.progress_file = os.path.join(self.results_dir, 'scraping_progress.json')
        self.pause_file = os.path.join(self.results_dir, 'PAUSE_SCRAPING')
        self.is_paused = False
        self.current_chunk = None
        
        # Create results directory
        os.makedirs(self.results_dir, exist_ok=True)
        
        # Initialize contact extractor
        self.extractor = PerfectContactExtractor(batch_size=50, max_concurrent=6)
        
        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Handle interrupt signals for graceful shutdown."""
        print(f"\n⚠️  Received signal {signum}. Pausing scraper...")
        self.pause()
    
    def pause(self):
        """Pause the scraper."""
        self.is_paused = True
        with open(self.pause_file, 'w') as f:
            f.write(f"Scraper paused at: {datetime.now().isoformat()}\n")
        print(f"⏸️  Scraper paused. Delete {self.pause_file} to resume.")
    
    def is_pause_requested(self) -> bool:
        """Check if pause is requested."""
        return os.path.exists(self.pause_file) or self.is_paused
    
    def load_progress(self) -> Dict:
        """Load scraping progress."""
        if os.path.exists(self.progress_file):
            with open(self.progress_file, 'r') as f:
                return json.load(f)
        return {
            'started_at': datetime.now().isoformat(),
            'last_updated': datetime.now().isoformat(),
            'completed_chunks': [],
            'current_chunk': None,
            'total_processed': 0,
            'total_with_emails': 0,
            'total_with_phones': 0
        }
    
    def save_progress(self, progress: Dict):
        """Save scraping progress."""
        progress['last_updated'] = datetime.now().isoformat()
        with open(self.progress_file, 'w') as f:
            json.dump(progress, f, indent=2)
    
    def load_chunk_manifest(self) -> Dict:
        """Load chunk manifest."""
        manifest_path = os.path.join(self.chunks_dir, 'chunk_manifest.json')
        if not os.path.exists(manifest_path):
            raise FileNotFoundError(f"Chunk manifest not found: {manifest_path}")
        
        with open(manifest_path, 'r') as f:
            return json.load(f)
    
    async def process_chunk(self, chunk_info: Dict) -> Dict:
        """Process a single chunk."""
        chunk_num = chunk_info['chunk_number']
        chunk_path = chunk_info['path']
        
        print(f"\n📦 Processing Chunk {chunk_num}")
        print(f"   📄 File: {os.path.basename(chunk_path)}")
        print(f"   🔗 URLs: {chunk_info['records']}")
        
        # Load chunk data
        chunk_df = pd.read_csv(chunk_path)
        urls = chunk_df['website'].tolist()
        
        # Extract contacts
        start_time = datetime.now()
        results = await self.extractor.extract_perfect(urls)
        duration = (datetime.now() - start_time).total_seconds()
        
        # Process results and merge with original data
        processed_results = []
        for i, result in enumerate(results):
            original_row = chunk_df.iloc[i].to_dict()
            
            # Merge original data with extracted contacts
            merged_result = {**original_row}
            
            if 'error' not in result:
                merged_result.update({
                    'extracted_email': result.get('email', {}).get('email', ''),
                    'extracted_phone': result.get('phone', {}).get('phone', ''),
                    'extracted_social_facebook': result.get('social', {}).get('facebook', ''),
                    'extracted_social_instagram': result.get('social', {}).get('instagram', ''),
                    'extracted_social_twitter': result.get('social', {}).get('twitter', ''),
                    'extraction_success': True,
                    'extraction_timestamp': result.get('timestamp', ''),
                    'pages_checked': result.get('pages_checked', 0),
                    'pages_with_content': result.get('pages_with_content', 0)
                })
            else:
                merged_result.update({
                    'extracted_email': '',
                    'extracted_phone': '',
                    'extracted_social_facebook': '',
                    'extracted_social_instagram': '',
                    'extracted_social_twitter': '',
                    'extraction_success': False,
                    'extraction_error': result.get('error', ''),
                    'extraction_timestamp': result.get('timestamp', ''),
                    'pages_checked': 0,
                    'pages_with_content': 0
                })
            
            processed_results.append(merged_result)
        
        # Save chunk results
        results_df = pd.DataFrame(processed_results)
        chunk_results_file = os.path.join(
            self.results_dir, 
            f"results_chunk_{chunk_num:03d}.csv"
        )
        results_df.to_csv(chunk_results_file, index=False)
        
        # Calculate statistics
        emails_found = sum(1 for r in processed_results if r.get('extracted_email', ''))
        phones_found = sum(1 for r in processed_results if r.get('extracted_phone', ''))
        
        chunk_stats = {
            'chunk_number': chunk_num,
            'urls_processed': len(urls),
            'emails_found': emails_found,
            'phones_found': phones_found,
            'processing_time': duration,
            'results_file': chunk_results_file,
            'completed_at': datetime.now().isoformat()
        }
        
        print(f"   ✅ Completed in {duration:.1f}s")
        print(f"   📧 Emails found: {emails_found}")
        print(f"   📞 Phones found: {phones_found}")
        print(f"   💾 Results saved: {os.path.basename(chunk_results_file)}")
        
        return chunk_stats
    
    async def run(self):
        """Run the pausable scraper."""
        print("🚀 PAUSABLE CONTACT SCRAPER")
        print("=" * 60)
        print(f"📂 Chunks directory: {self.chunks_dir}")
        print(f"📂 Results directory: {self.results_dir}")
        print(f"⏸️  To pause: Create file '{self.pause_file}' or press Ctrl+C")
        
        # Load manifest and progress
        manifest = self.load_chunk_manifest()
        progress = self.load_progress()
        
        total_chunks = manifest['num_chunks']
        completed_chunks = set(progress['completed_chunks'])
        
        print(f"\n📊 Status:")
        print(f"   • Total chunks: {total_chunks}")
        print(f"   • Completed: {len(completed_chunks)}")
        print(f"   • Remaining: {total_chunks - len(completed_chunks)}")
        
        if len(completed_chunks) == total_chunks:
            print("✅ All chunks already completed!")
            return
        
        # Process remaining chunks
        for chunk_info in manifest['chunks']:
            chunk_num = chunk_info['chunk_number']
            
            # Skip completed chunks
            if chunk_num in completed_chunks:
                continue
            
            # Check for pause request
            if self.is_pause_requested():
                print(f"\n⏸️  Pausing at chunk {chunk_num}")
                break
            
            self.current_chunk = chunk_num
            progress['current_chunk'] = chunk_num
            self.save_progress(progress)
            
            try:
                # Process chunk
                chunk_stats = await self.process_chunk(chunk_info)
                
                # Update progress
                progress['completed_chunks'].append(chunk_num)
                progress['total_processed'] += chunk_stats['urls_processed']
                progress['total_with_emails'] += chunk_stats['emails_found']
                progress['total_with_phones'] += chunk_stats['phones_found']
                progress['current_chunk'] = None
                
                self.save_progress(progress)
                
                print(f"   📈 Overall progress: {len(progress['completed_chunks'])}/{total_chunks} chunks")
                
            except Exception as e:
                print(f"❌ Error processing chunk {chunk_num}: {str(e)}")
                # Continue with next chunk
                continue
        
        # Final summary
        if len(progress['completed_chunks']) == total_chunks:
            print(f"\n🎉 SCRAPING COMPLETED!")
        else:
            print(f"\n⏸️  SCRAPING PAUSED")
        
        print(f"   • Chunks completed: {len(progress['completed_chunks'])}/{total_chunks}")
        print(f"   • URLs processed: {progress['total_processed']:,}")
        print(f"   • Emails found: {progress['total_with_emails']:,}")
        print(f"   • Phones found: {progress['total_with_phones']:,}")
        print(f"   • Results directory: {self.results_dir}")

def main():
    """Main function."""
    # Look for chunk directories
    chunk_dirs = [d for d in os.listdir('.') if d.endswith('_chunks') and os.path.isdir(d)]
    
    if not chunk_dirs:
        print("❌ No chunk directories found!")
        print("Please run dataset_chunker.py first to create chunks.")
        return
    
    if len(chunk_dirs) > 1:
        print("🤔 Multiple chunk directories found:")
        for i, dir_name in enumerate(chunk_dirs, 1):
            print(f"   {i}. {dir_name}")
        
        try:
            choice = int(input("Select directory number: ")) - 1
            chunks_dir = chunk_dirs[choice]
        except (ValueError, IndexError):
            print("Invalid choice. Using first directory.")
            chunks_dir = chunk_dirs[0]
    else:
        chunks_dir = chunk_dirs[0]
    
    print(f"📂 Using chunks directory: {chunks_dir}")
    
    # Create and run scraper
    scraper = PausableScraper(chunks_dir)
    
    try:
        asyncio.run(scraper.run())
    except KeyboardInterrupt:
        print("\n⚠️  Interrupted by user")
    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    main()
